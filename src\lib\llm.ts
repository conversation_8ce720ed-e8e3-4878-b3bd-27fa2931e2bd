import OpenAI from 'openai';
import { Finding, LLMExplanation } from '@/types';

export class LLMExplainer {
  private openai: OpenAI;

  constructor(apiKey?: string) {
    this.openai = new OpenAI({
      apiKey: apiKey || process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Generate detailed explanation for a security finding
   */
  async explainFinding(finding: Finding, codeContext?: string): Promise<LLMExplanation> {
    const prompt = this.buildExplanationPrompt(finding, codeContext);

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are a cybersecurity expert specializing in code analysis and vulnerability assessment. 
            Provide clear, actionable explanations of security vulnerabilities found in code.
            Focus on practical impact and remediation steps.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
      });

      const content = response.choices[0]?.message?.content || '';
      return this.parseExplanationResponse(finding, content);
    } catch (error) {
      console.error('Error generating LLM explanation:', error);
      return this.getFallbackExplanation(finding);
    }
  }

  /**
   * Generate explanations for multiple findings
   */
  async explainFindings(findings: Finding[]): Promise<LLMExplanation[]> {
    const explanations: LLMExplanation[] = [];
    
    // Process in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < findings.length; i += batchSize) {
      const batch = findings.slice(i, i + batchSize);
      const batchPromises = batch.map(finding => this.explainFinding(finding));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        explanations.push(...batchResults);
      } catch (error) {
        console.error('Error processing batch:', error);
        // Add fallback explanations for failed batch
        batch.forEach(finding => {
          explanations.push(this.getFallbackExplanation(finding));
        });
      }
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < findings.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return explanations;
  }

  /**
   * Build prompt for LLM explanation
   */
  private buildExplanationPrompt(finding: Finding, codeContext?: string): string {
    return `
Analyze this security vulnerability found in code:

**Vulnerability Details:**
- Rule: ${finding.ruleName}
- Severity: ${finding.severity.toUpperCase()}
- Category: ${finding.category}
- CWE: ${finding.cwe || 'N/A'}
- Description: ${finding.description}

**Detected Code:**
\`\`\`
${finding.code}
\`\`\`

**Code Context:**
\`\`\`
${codeContext || finding.context}
\`\`\`

**Location:** Line ${finding.line}${finding.column ? `, Column ${finding.column}` : ''}

Please provide a comprehensive analysis including:

1. **Explanation**: What exactly is the security issue and why is it dangerous?
2. **Impact**: What could an attacker achieve by exploiting this vulnerability?
3. **Remediation**: Specific steps to fix this vulnerability with code examples if applicable.
4. **Confidence**: Your confidence level (0-100) in this assessment.

Format your response as:
EXPLANATION: [detailed explanation]
IMPACT: [potential impact]
REMEDIATION: [specific fix instructions]
CONFIDENCE: [0-100]
`;
  }

  /**
   * Parse LLM response into structured explanation
   */
  private parseExplanationResponse(finding: Finding, content: string): LLMExplanation {
    const sections = {
      explanation: '',
      impact: '',
      remediation: '',
      confidence: 80
    };

    // Parse structured response
    const explanationMatch = content.match(/EXPLANATION:\s*([\s\S]*?)(?=IMPACT:|$)/);
    const impactMatch = content.match(/IMPACT:\s*([\s\S]*?)(?=REMEDIATION:|$)/);
    const remediationMatch = content.match(/REMEDIATION:\s*([\s\S]*?)(?=CONFIDENCE:|$)/);
    const confidenceMatch = content.match(/CONFIDENCE:\s*(\d+)/);

    if (explanationMatch) {
      sections.explanation = explanationMatch[1].trim();
    }
    if (impactMatch) {
      sections.impact = impactMatch[1].trim();
    }
    if (remediationMatch) {
      sections.remediation = remediationMatch[1].trim();
    }
    if (confidenceMatch) {
      sections.confidence = parseInt(confidenceMatch[1], 10);
    }

    // Fallback to full content if parsing fails
    if (!sections.explanation && !sections.impact && !sections.remediation) {
      sections.explanation = content;
      sections.impact = 'Impact analysis not available';
      sections.remediation = finding.remediation;
    }

    return {
      finding,
      explanation: sections.explanation || finding.description,
      impact: sections.impact || this.getDefaultImpact(finding.severity),
      remediation: sections.remediation || finding.remediation,
      confidence: sections.confidence
    };
  }

  /**
   * Get fallback explanation when LLM fails
   */
  private getFallbackExplanation(finding: Finding): LLMExplanation {
    return {
      finding,
      explanation: finding.description,
      impact: this.getDefaultImpact(finding.severity),
      remediation: finding.remediation,
      confidence: 60
    };
  }

  /**
   * Get default impact description based on severity
   */
  private getDefaultImpact(severity: string): string {
    const impactMap: Record<string, string> = {
      critical: 'This vulnerability could lead to complete system compromise, data theft, or remote code execution.',
      high: 'This vulnerability could allow attackers to gain unauthorized access or execute malicious code.',
      medium: 'This vulnerability could be exploited to bypass security controls or access sensitive information.',
      low: 'This vulnerability represents a minor security concern that should be addressed as part of security hardening.'
    };

    return impactMap[severity] || 'Security impact assessment not available.';
  }

  /**
   * Generate security report summary
   */
  async generateSecurityReport(findings: Finding[]): Promise<string> {
    if (findings.length === 0) {
      return 'No security vulnerabilities detected in the scanned code.';
    }

    const prompt = `
Generate a comprehensive security report for the following vulnerabilities found in code:

${findings.map((finding, index) => `
${index + 1}. ${finding.ruleName} (${finding.severity.toUpperCase()})
   - Category: ${finding.category}
   - Location: Line ${finding.line}
   - Code: ${finding.code}
`).join('')}

Provide:
1. Executive Summary
2. Risk Assessment
3. Priority Recommendations
4. Overall Security Posture

Keep the report concise but comprehensive.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are a cybersecurity analyst creating executive security reports.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.3,
      });

      return response.choices[0]?.message?.content || 'Unable to generate security report.';
    } catch (error) {
      console.error('Error generating security report:', error);
      return this.generateFallbackReport(findings);
    }
  }

  /**
   * Generate fallback report when LLM fails
   */
  private generateFallbackReport(findings: Finding[]): string {
    const severityCounts = findings.reduce((acc, finding) => {
      acc[finding.severity] = (acc[finding.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return `
# Security Scan Report

## Executive Summary
The code scan identified ${findings.length} security vulnerabilities across different severity levels.

## Vulnerability Breakdown
- Critical: ${severityCounts.critical || 0}
- High: ${severityCounts.high || 0}
- Medium: ${severityCounts.medium || 0}
- Low: ${severityCounts.low || 0}

## Recommendations
1. Address critical and high severity vulnerabilities immediately
2. Review and remediate medium severity issues
3. Plan to fix low severity issues in upcoming releases
4. Implement security code review processes

## Next Steps
- Prioritize fixes based on severity and exploitability
- Implement automated security scanning in CI/CD pipeline
- Conduct regular security assessments
`;
  }
}
