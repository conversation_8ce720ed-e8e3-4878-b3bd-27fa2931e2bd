/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cmcpsafe%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cmcpsafe&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cmcpsafe%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cmcpsafe&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cmcpsafe%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cmcpsafe&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDbWNwc2FmZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUFvRyIsInNvdXJjZXMiOlsid2VicGFjazovL21jcHNhZmUvP2EzMTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0XFxcXG1jcHNhZmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDbWNwc2FmZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdCU1QyU1Q21jcHNhZmUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdCU1QyU1Q21jcHNhZmUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJOYXZpZ2F0aW9uJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBMEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tY3BzYWZlLz9hODRjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTmF2aWdhdGlvblwiXSAqLyBcIkQ6XFxcXFByb2plY3RcXFxcbWNwc2FmZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxOYXZpZ2F0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cmcpsafe%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,FileText,Github,Home,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,FileText,Github,Home,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,FileText,Github,Home,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,FileText,Github,Home,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,FileText,Github,Home,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Code Scanner\",\n        href: \"/scan\",\n        icon: _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"GitHub Scanner\",\n        href: \"/github\",\n        icon: _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Documentation\",\n        href: \"/docs\",\n        icon: _barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    }\n];\nfunction Navigation() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_FileText_Github_Home_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"MCP Safe\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                    children: navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        const isActive = pathname === item.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium space-x-1\", isActive ? \"border-blue-500 text-gray-900\" : \"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Secure Code Analysis Platform\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-2 pb-3 space-y-1\",\n                    children: navigation.map((item)=>{\n                        const Icon = item.icon;\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center space-x-2 pl-3 pr-4 py-2 border-l-4 text-base font-medium\", isActive ? \"bg-blue-50 border-blue-500 text-blue-700\" : \"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadFile: () => (/* binding */ downloadFile),\n/* harmony export */   exportScanResults: () => (/* binding */ exportScanResults),\n/* harmony export */   exportScanResultsToCSV: () => (/* binding */ exportScanResultsToCSV),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateReportFilename: () => (/* binding */ generateReportFilename),\n/* harmony export */   getRiskLevel: () => (/* binding */ getRiskLevel),\n/* harmony export */   getRiskScoreColor: () => (/* binding */ getRiskScoreColor),\n/* harmony export */   getSeverityBadgeColor: () => (/* binding */ getSeverityBadgeColor),\n/* harmony export */   getSeverityColor: () => (/* binding */ getSeverityColor),\n/* harmony export */   groupFindingsByCategory: () => (/* binding */ groupFindingsByCategory),\n/* harmony export */   groupFindingsBySeverity: () => (/* binding */ groupFindingsBySeverity),\n/* harmony export */   isValidGitHubUrl: () => (/* binding */ isValidGitHubUrl),\n/* harmony export */   sanitizeFilename: () => (/* binding */ sanitizeFilename),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format file size in human readable format\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * Format date in relative time\n */ function formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"just now\";\n    } else if (diffInSeconds < 3600) {\n        const minutes = Math.floor(diffInSeconds / 60);\n        return `${minutes} minute${minutes > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 86400) {\n        const hours = Math.floor(diffInSeconds / 3600);\n        return `${hours} hour${hours > 1 ? \"s\" : \"\"} ago`;\n    } else {\n        const days = Math.floor(diffInSeconds / 86400);\n        return `${days} day${days > 1 ? \"s\" : \"\"} ago`;\n    }\n}\n/**\n * Get severity color for UI components\n */ function getSeverityColor(severity) {\n    const colorMap = {\n        critical: \"text-red-600 bg-red-50 border-red-200\",\n        high: \"text-orange-600 bg-orange-50 border-orange-200\",\n        medium: \"text-yellow-600 bg-yellow-50 border-yellow-200\",\n        low: \"text-blue-600 bg-blue-50 border-blue-200\"\n    };\n    return colorMap[severity] || \"text-gray-600 bg-gray-50 border-gray-200\";\n}\n/**\n * Get severity badge color\n */ function getSeverityBadgeColor(severity) {\n    const colorMap = {\n        critical: \"bg-red-100 text-red-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        medium: \"bg-yellow-100 text-yellow-800\",\n        low: \"bg-blue-100 text-blue-800\"\n    };\n    return colorMap[severity] || \"bg-gray-100 text-gray-800\";\n}\n/**\n * Export scan results to JSON\n */ function exportScanResults(scanResult) {\n    return JSON.stringify(scanResult, null, 2);\n}\n/**\n * Export scan results to CSV\n */ function exportScanResultsToCSV(scanResult) {\n    const headers = [\n        \"Rule Name\",\n        \"Severity\",\n        \"Category\",\n        \"Line\",\n        \"Column\",\n        \"Code\",\n        \"Description\",\n        \"CWE\"\n    ];\n    const rows = scanResult.findings.map((finding)=>[\n            finding.ruleName,\n            finding.severity,\n            finding.category,\n            finding.line.toString(),\n            finding.column?.toString() || \"\",\n            `\"${finding.code.replace(/\"/g, '\"\"')}\"`,\n            `\"${finding.description.replace(/\"/g, '\"\"')}\"`,\n            finding.cwe || \"\"\n        ]);\n    return [\n        headers.join(\",\"),\n        ...rows.map((row)=>row.join(\",\"))\n    ].join(\"\\n\");\n}\n/**\n * Download file with given content\n */ function downloadFile(content, filename, contentType = \"text/plain\") {\n    const blob = new Blob([\n        content\n    ], {\n        type: contentType\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n}\n/**\n * Validate GitHub URL\n */ function isValidGitHubUrl(url) {\n    const githubUrlPattern = /^https?:\\/\\/github\\.com\\/[^\\/]+\\/[^\\/]+/;\n    return githubUrlPattern.test(url);\n}\n/**\n * Sanitize filename for download\n */ function sanitizeFilename(filename) {\n    return filename.replace(/[^a-z0-9.-]/gi, \"_\").toLowerCase();\n}\n/**\n * Group findings by category\n */ function groupFindingsByCategory(findings) {\n    return findings.reduce((groups, finding)=>{\n        const category = finding.category;\n        if (!groups[category]) {\n            groups[category] = [];\n        }\n        groups[category].push(finding);\n        return groups;\n    }, {});\n}\n/**\n * Group findings by severity\n */ function groupFindingsBySeverity(findings) {\n    return findings.reduce((groups, finding)=>{\n        const severity = finding.severity;\n        if (!groups[severity]) {\n            groups[severity] = [];\n        }\n        groups[severity].push(finding);\n        return groups;\n    }, {});\n}\n/**\n * Calculate risk score color\n */ function getRiskScoreColor(score) {\n    if (score >= 80) return \"text-red-600\";\n    if (score >= 60) return \"text-orange-600\";\n    if (score >= 40) return \"text-yellow-600\";\n    return \"text-green-600\";\n}\n/**\n * Get risk level from score\n */ function getRiskLevel(score) {\n    if (score >= 80) return \"Critical\";\n    if (score >= 60) return \"High\";\n    if (score >= 40) return \"Medium\";\n    return \"Low\";\n}\n/**\n * Truncate text with ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength - 3) + \"...\";\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * Generate scan report filename\n */ function generateReportFilename(scanResult, format = \"json\") {\n    const timestamp = scanResult.timestamp.toISOString().split(\"T\")[0];\n    const source = scanResult.sourceUrl ? sanitizeFilename(scanResult.sourceUrl.split(\"/\").pop() || \"scan\") : \"manual-scan\";\n    return `mcp-security-scan-${source}-${timestamp}.${format}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFHbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRUE7O0NBRUMsR0FDTSxTQUFTQyxlQUFlQyxLQUFhO0lBQzFDLElBQUlBLFVBQVUsR0FBRyxPQUFPO0lBRXhCLE1BQU1DLElBQUk7SUFDVixNQUFNQyxRQUFRO1FBQUM7UUFBUztRQUFNO1FBQU07S0FBSztJQUN6QyxNQUFNQyxJQUFJQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLEdBQUcsQ0FBQ04sU0FBU0ksS0FBS0UsR0FBRyxDQUFDTDtJQUVoRCxPQUFPTSxXQUFXLENBQUNQLFFBQVFJLEtBQUtJLEdBQUcsQ0FBQ1AsR0FBR0UsRUFBQyxFQUFHTSxPQUFPLENBQUMsTUFBTSxNQUFNUCxLQUFLLENBQUNDLEVBQUU7QUFDekU7QUFFQTs7Q0FFQyxHQUNNLFNBQVNPLG1CQUFtQkMsSUFBVTtJQUMzQyxNQUFNQyxNQUFNLElBQUlDO0lBQ2hCLE1BQU1DLGdCQUFnQlYsS0FBS0MsS0FBSyxDQUFDLENBQUNPLElBQUlHLE9BQU8sS0FBS0osS0FBS0ksT0FBTyxFQUFDLElBQUs7SUFFcEUsSUFBSUQsZ0JBQWdCLElBQUk7UUFDdEIsT0FBTztJQUNULE9BQU8sSUFBSUEsZ0JBQWdCLE1BQU07UUFDL0IsTUFBTUUsVUFBVVosS0FBS0MsS0FBSyxDQUFDUyxnQkFBZ0I7UUFDM0MsT0FBTyxDQUFDLEVBQUVFLFFBQVEsT0FBTyxFQUFFQSxVQUFVLElBQUksTUFBTSxHQUFHLElBQUksQ0FBQztJQUN6RCxPQUFPLElBQUlGLGdCQUFnQixPQUFPO1FBQ2hDLE1BQU1HLFFBQVFiLEtBQUtDLEtBQUssQ0FBQ1MsZ0JBQWdCO1FBQ3pDLE9BQU8sQ0FBQyxFQUFFRyxNQUFNLEtBQUssRUFBRUEsUUFBUSxJQUFJLE1BQU0sR0FBRyxJQUFJLENBQUM7SUFDbkQsT0FBTztRQUNMLE1BQU1DLE9BQU9kLEtBQUtDLEtBQUssQ0FBQ1MsZ0JBQWdCO1FBQ3hDLE9BQU8sQ0FBQyxFQUFFSSxLQUFLLElBQUksRUFBRUEsT0FBTyxJQUFJLE1BQU0sR0FBRyxJQUFJLENBQUM7SUFDaEQ7QUFDRjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsaUJBQWlCQyxRQUFnQjtJQUMvQyxNQUFNQyxXQUFtQztRQUN2Q0MsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztJQUNQO0lBRUEsT0FBT0osUUFBUSxDQUFDRCxTQUFTLElBQUk7QUFDL0I7QUFFQTs7Q0FFQyxHQUNNLFNBQVNNLHNCQUFzQk4sUUFBZ0I7SUFDcEQsTUFBTUMsV0FBbUM7UUFDdkNDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7SUFDUDtJQUVBLE9BQU9KLFFBQVEsQ0FBQ0QsU0FBUyxJQUFJO0FBQy9CO0FBRUE7O0NBRUMsR0FDTSxTQUFTTyxrQkFBa0JDLFVBQXNCO0lBQ3RELE9BQU9DLEtBQUtDLFNBQVMsQ0FBQ0YsWUFBWSxNQUFNO0FBQzFDO0FBRUE7O0NBRUMsR0FDTSxTQUFTRyx1QkFBdUJILFVBQXNCO0lBQzNELE1BQU1JLFVBQVU7UUFDZDtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRCxNQUFNQyxPQUFPTCxXQUFXTSxRQUFRLENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUEsVUFBVztZQUM5Q0EsUUFBUUMsUUFBUTtZQUNoQkQsUUFBUWhCLFFBQVE7WUFDaEJnQixRQUFRRSxRQUFRO1lBQ2hCRixRQUFRRyxJQUFJLENBQUNDLFFBQVE7WUFDckJKLFFBQVFLLE1BQU0sRUFBRUQsY0FBYztZQUM5QixDQUFDLENBQUMsRUFBRUosUUFBUU0sSUFBSSxDQUFDQyxPQUFPLENBQUMsTUFBTSxNQUFNLENBQUMsQ0FBQztZQUN2QyxDQUFDLENBQUMsRUFBRVAsUUFBUVEsV0FBVyxDQUFDRCxPQUFPLENBQUMsTUFBTSxNQUFNLENBQUMsQ0FBQztZQUM5Q1AsUUFBUVMsR0FBRyxJQUFJO1NBQ2hCO0lBRUQsT0FBTztRQUFDYixRQUFRYyxJQUFJLENBQUM7V0FBU2IsS0FBS0UsR0FBRyxDQUFDWSxDQUFBQSxNQUFPQSxJQUFJRCxJQUFJLENBQUM7S0FBTSxDQUFDQSxJQUFJLENBQUM7QUFDckU7QUFFQTs7Q0FFQyxHQUNNLFNBQVNFLGFBQWFDLE9BQWUsRUFBRUMsUUFBZ0IsRUFBRUMsY0FBc0IsWUFBWTtJQUNoRyxNQUFNQyxPQUFPLElBQUlDLEtBQUs7UUFBQ0o7S0FBUSxFQUFFO1FBQUVLLE1BQU1IO0lBQVk7SUFDckQsTUFBTUksTUFBTUMsSUFBSUMsZUFBZSxDQUFDTDtJQUVoQyxNQUFNTSxPQUFPQyxTQUFTQyxhQUFhLENBQUM7SUFDcENGLEtBQUtHLElBQUksR0FBR047SUFDWkcsS0FBS0ksUUFBUSxHQUFHWjtJQUNoQlMsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNOO0lBQzFCQSxLQUFLTyxLQUFLO0lBQ1ZOLFNBQVNJLElBQUksQ0FBQ0csV0FBVyxDQUFDUjtJQUUxQkYsSUFBSVcsZUFBZSxDQUFDWjtBQUN0QjtBQUVBOztDQUVDLEdBQ00sU0FBU2EsaUJBQWlCYixHQUFXO0lBQzFDLE1BQU1jLG1CQUFtQjtJQUN6QixPQUFPQSxpQkFBaUJDLElBQUksQ0FBQ2Y7QUFDL0I7QUFFQTs7Q0FFQyxHQUNNLFNBQVNnQixpQkFBaUJyQixRQUFnQjtJQUMvQyxPQUFPQSxTQUFTUCxPQUFPLENBQUMsaUJBQWlCLEtBQUs2QixXQUFXO0FBQzNEO0FBRUE7O0NBRUMsR0FDTSxTQUFTQyx3QkFBd0J2QyxRQUFtQjtJQUN6RCxPQUFPQSxTQUFTd0MsTUFBTSxDQUFDLENBQUNDLFFBQVF2QztRQUM5QixNQUFNRSxXQUFXRixRQUFRRSxRQUFRO1FBQ2pDLElBQUksQ0FBQ3FDLE1BQU0sQ0FBQ3JDLFNBQVMsRUFBRTtZQUNyQnFDLE1BQU0sQ0FBQ3JDLFNBQVMsR0FBRyxFQUFFO1FBQ3ZCO1FBQ0FxQyxNQUFNLENBQUNyQyxTQUFTLENBQUNzQyxJQUFJLENBQUN4QztRQUN0QixPQUFPdUM7SUFDVCxHQUFHLENBQUM7QUFDTjtBQUVBOztDQUVDLEdBQ00sU0FBU0Usd0JBQXdCM0MsUUFBbUI7SUFDekQsT0FBT0EsU0FBU3dDLE1BQU0sQ0FBQyxDQUFDQyxRQUFRdkM7UUFDOUIsTUFBTWhCLFdBQVdnQixRQUFRaEIsUUFBUTtRQUNqQyxJQUFJLENBQUN1RCxNQUFNLENBQUN2RCxTQUFTLEVBQUU7WUFDckJ1RCxNQUFNLENBQUN2RCxTQUFTLEdBQUcsRUFBRTtRQUN2QjtRQUNBdUQsTUFBTSxDQUFDdkQsU0FBUyxDQUFDd0QsSUFBSSxDQUFDeEM7UUFDdEIsT0FBT3VDO0lBQ1QsR0FBRyxDQUFDO0FBQ047QUFFQTs7Q0FFQyxHQUNNLFNBQVNHLGtCQUFrQkMsS0FBYTtJQUM3QyxJQUFJQSxTQUFTLElBQUksT0FBTztJQUN4QixJQUFJQSxTQUFTLElBQUksT0FBTztJQUN4QixJQUFJQSxTQUFTLElBQUksT0FBTztJQUN4QixPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDLGFBQWFELEtBQWE7SUFDeEMsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsT0FBTztBQUNUO0FBRUE7O0NBRUMsR0FDTSxTQUFTRSxhQUFhQyxJQUFZLEVBQUVDLFNBQWlCO0lBQzFELElBQUlELEtBQUtFLE1BQU0sSUFBSUQsV0FBVyxPQUFPRDtJQUNyQyxPQUFPQSxLQUFLRyxTQUFTLENBQUMsR0FBR0YsWUFBWSxLQUFLO0FBQzVDO0FBRUE7O0NBRUMsR0FDTSxTQUFTRyxTQUNkQyxJQUFPLEVBQ1BDLElBQVk7SUFFWixJQUFJQztJQUVKLE9BQU8sQ0FBQyxHQUFHQztRQUNUQyxhQUFhRjtRQUNiQSxVQUFVRyxXQUFXLElBQU1MLFFBQVFHLE9BQU9GO0lBQzVDO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVLLGdCQUFnQlgsSUFBWTtJQUNoRCxJQUFJO1FBQ0YsTUFBTVksVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNkO1FBQ3BDLE9BQU87SUFDVCxFQUFFLE9BQU9lLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNFLHVCQUF1QnZFLFVBQXNCLEVBQUV3RSxTQUF5QixNQUFNO0lBQzVGLE1BQU1DLFlBQVl6RSxXQUFXeUUsU0FBUyxDQUFDQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtJQUNsRSxNQUFNQyxTQUFTNUUsV0FBVzZFLFNBQVMsR0FDakNsQyxpQkFBaUIzQyxXQUFXNkUsU0FBUyxDQUFDRixLQUFLLENBQUMsS0FBS0csR0FBRyxNQUFNLFVBQzFEO0lBRUYsT0FBTyxDQUFDLGtCQUFrQixFQUFFRixPQUFPLENBQUMsRUFBRUgsVUFBVSxDQUFDLEVBQUVELE9BQU8sQ0FBQztBQUM3RCIsInNvdXJjZXMiOlsid2VicGFjazovL21jcHNhZmUvLi9zcmMvbGliL3V0aWxzLnRzPzdjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIjtcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIjtcbmltcG9ydCB7IFNjYW5SZXN1bHQsIEZpbmRpbmcgfSBmcm9tICdAL3R5cGVzJztcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG5cbi8qKlxuICogRm9ybWF0IGZpbGUgc2l6ZSBpbiBodW1hbiByZWFkYWJsZSBmb3JtYXRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEZpbGVTaXplKGJ5dGVzOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAoYnl0ZXMgPT09IDApIHJldHVybiAnMCBCeXRlcyc7XG4gIFxuICBjb25zdCBrID0gMTAyNDtcbiAgY29uc3Qgc2l6ZXMgPSBbJ0J5dGVzJywgJ0tCJywgJ01CJywgJ0dCJ107XG4gIGNvbnN0IGkgPSBNYXRoLmZsb29yKE1hdGgubG9nKGJ5dGVzKSAvIE1hdGgubG9nKGspKTtcbiAgXG4gIHJldHVybiBwYXJzZUZsb2F0KChieXRlcyAvIE1hdGgucG93KGssIGkpKS50b0ZpeGVkKDIpKSArICcgJyArIHNpemVzW2ldO1xufVxuXG4vKipcbiAqIEZvcm1hdCBkYXRlIGluIHJlbGF0aXZlIHRpbWVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFJlbGF0aXZlVGltZShkYXRlOiBEYXRlKTogc3RyaW5nIHtcbiAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcbiAgY29uc3QgZGlmZkluU2Vjb25kcyA9IE1hdGguZmxvb3IoKG5vdy5nZXRUaW1lKCkgLSBkYXRlLmdldFRpbWUoKSkgLyAxMDAwKTtcbiAgXG4gIGlmIChkaWZmSW5TZWNvbmRzIDwgNjApIHtcbiAgICByZXR1cm4gJ2p1c3Qgbm93JztcbiAgfSBlbHNlIGlmIChkaWZmSW5TZWNvbmRzIDwgMzYwMCkge1xuICAgIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKGRpZmZJblNlY29uZHMgLyA2MCk7XG4gICAgcmV0dXJuIGAke21pbnV0ZXN9IG1pbnV0ZSR7bWludXRlcyA+IDEgPyAncycgOiAnJ30gYWdvYDtcbiAgfSBlbHNlIGlmIChkaWZmSW5TZWNvbmRzIDwgODY0MDApIHtcbiAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDM2MDApO1xuICAgIHJldHVybiBgJHtob3Vyc30gaG91ciR7aG91cnMgPiAxID8gJ3MnIDogJyd9IGFnb2A7XG4gIH0gZWxzZSB7XG4gICAgY29uc3QgZGF5cyA9IE1hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDg2NDAwKTtcbiAgICByZXR1cm4gYCR7ZGF5c30gZGF5JHtkYXlzID4gMSA/ICdzJyA6ICcnfSBhZ29gO1xuICB9XG59XG5cbi8qKlxuICogR2V0IHNldmVyaXR5IGNvbG9yIGZvciBVSSBjb21wb25lbnRzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRTZXZlcml0eUNvbG9yKHNldmVyaXR5OiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBjb2xvck1hcDogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICBjcml0aWNhbDogJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAnLFxuICAgIGhpZ2g6ICd0ZXh0LW9yYW5nZS02MDAgYmctb3JhbmdlLTUwIGJvcmRlci1vcmFuZ2UtMjAwJyxcbiAgICBtZWRpdW06ICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTUwIGJvcmRlci15ZWxsb3ctMjAwJyxcbiAgICBsb3c6ICd0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJyxcbiAgfTtcbiAgXG4gIHJldHVybiBjb2xvck1hcFtzZXZlcml0eV0gfHwgJ3RleHQtZ3JheS02MDAgYmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAnO1xufVxuXG4vKipcbiAqIEdldCBzZXZlcml0eSBiYWRnZSBjb2xvclxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0U2V2ZXJpdHlCYWRnZUNvbG9yKHNldmVyaXR5OiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBjb2xvck1hcDogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICBjcml0aWNhbDogJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyxcbiAgICBoaWdoOiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAnLFxuICAgIG1lZGl1bTogJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJyxcbiAgICBsb3c6ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyxcbiAgfTtcbiAgXG4gIHJldHVybiBjb2xvck1hcFtzZXZlcml0eV0gfHwgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xufVxuXG4vKipcbiAqIEV4cG9ydCBzY2FuIHJlc3VsdHMgdG8gSlNPTlxuICovXG5leHBvcnQgZnVuY3Rpb24gZXhwb3J0U2NhblJlc3VsdHMoc2NhblJlc3VsdDogU2NhblJlc3VsdCk6IHN0cmluZyB7XG4gIHJldHVybiBKU09OLnN0cmluZ2lmeShzY2FuUmVzdWx0LCBudWxsLCAyKTtcbn1cblxuLyoqXG4gKiBFeHBvcnQgc2NhbiByZXN1bHRzIHRvIENTVlxuICovXG5leHBvcnQgZnVuY3Rpb24gZXhwb3J0U2NhblJlc3VsdHNUb0NTVihzY2FuUmVzdWx0OiBTY2FuUmVzdWx0KTogc3RyaW5nIHtcbiAgY29uc3QgaGVhZGVycyA9IFtcbiAgICAnUnVsZSBOYW1lJyxcbiAgICAnU2V2ZXJpdHknLFxuICAgICdDYXRlZ29yeScsXG4gICAgJ0xpbmUnLFxuICAgICdDb2x1bW4nLFxuICAgICdDb2RlJyxcbiAgICAnRGVzY3JpcHRpb24nLFxuICAgICdDV0UnXG4gIF07XG4gIFxuICBjb25zdCByb3dzID0gc2NhblJlc3VsdC5maW5kaW5ncy5tYXAoZmluZGluZyA9PiBbXG4gICAgZmluZGluZy5ydWxlTmFtZSxcbiAgICBmaW5kaW5nLnNldmVyaXR5LFxuICAgIGZpbmRpbmcuY2F0ZWdvcnksXG4gICAgZmluZGluZy5saW5lLnRvU3RyaW5nKCksXG4gICAgZmluZGluZy5jb2x1bW4/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgYFwiJHtmaW5kaW5nLmNvZGUucmVwbGFjZSgvXCIvZywgJ1wiXCInKX1cImAsXG4gICAgYFwiJHtmaW5kaW5nLmRlc2NyaXB0aW9uLnJlcGxhY2UoL1wiL2csICdcIlwiJyl9XCJgLFxuICAgIGZpbmRpbmcuY3dlIHx8ICcnXG4gIF0pO1xuICBcbiAgcmV0dXJuIFtoZWFkZXJzLmpvaW4oJywnKSwgLi4ucm93cy5tYXAocm93ID0+IHJvdy5qb2luKCcsJykpXS5qb2luKCdcXG4nKTtcbn1cblxuLyoqXG4gKiBEb3dubG9hZCBmaWxlIHdpdGggZ2l2ZW4gY29udGVudFxuICovXG5leHBvcnQgZnVuY3Rpb24gZG93bmxvYWRGaWxlKGNvbnRlbnQ6IHN0cmluZywgZmlsZW5hbWU6IHN0cmluZywgY29udGVudFR5cGU6IHN0cmluZyA9ICd0ZXh0L3BsYWluJyk6IHZvaWQge1xuICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NvbnRlbnRdLCB7IHR5cGU6IGNvbnRlbnRUeXBlIH0pO1xuICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICBcbiAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgbGluay5ocmVmID0gdXJsO1xuICBsaW5rLmRvd25sb2FkID0gZmlsZW5hbWU7XG4gIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7XG4gIGxpbmsuY2xpY2soKTtcbiAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKTtcbiAgXG4gIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbn1cblxuLyoqXG4gKiBWYWxpZGF0ZSBHaXRIdWIgVVJMXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkR2l0SHViVXJsKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IGdpdGh1YlVybFBhdHRlcm4gPSAvXmh0dHBzPzpcXC9cXC9naXRodWJcXC5jb21cXC9bXlxcL10rXFwvW15cXC9dKy87XG4gIHJldHVybiBnaXRodWJVcmxQYXR0ZXJuLnRlc3QodXJsKTtcbn1cblxuLyoqXG4gKiBTYW5pdGl6ZSBmaWxlbmFtZSBmb3IgZG93bmxvYWRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNhbml0aXplRmlsZW5hbWUoZmlsZW5hbWU6IHN0cmluZyk6IHN0cmluZyB7XG4gIHJldHVybiBmaWxlbmFtZS5yZXBsYWNlKC9bXmEtejAtOS4tXS9naSwgJ18nKS50b0xvd2VyQ2FzZSgpO1xufVxuXG4vKipcbiAqIEdyb3VwIGZpbmRpbmdzIGJ5IGNhdGVnb3J5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBncm91cEZpbmRpbmdzQnlDYXRlZ29yeShmaW5kaW5nczogRmluZGluZ1tdKTogUmVjb3JkPHN0cmluZywgRmluZGluZ1tdPiB7XG4gIHJldHVybiBmaW5kaW5ncy5yZWR1Y2UoKGdyb3VwcywgZmluZGluZykgPT4ge1xuICAgIGNvbnN0IGNhdGVnb3J5ID0gZmluZGluZy5jYXRlZ29yeTtcbiAgICBpZiAoIWdyb3Vwc1tjYXRlZ29yeV0pIHtcbiAgICAgIGdyb3Vwc1tjYXRlZ29yeV0gPSBbXTtcbiAgICB9XG4gICAgZ3JvdXBzW2NhdGVnb3J5XS5wdXNoKGZpbmRpbmcpO1xuICAgIHJldHVybiBncm91cHM7XG4gIH0sIHt9IGFzIFJlY29yZDxzdHJpbmcsIEZpbmRpbmdbXT4pO1xufVxuXG4vKipcbiAqIEdyb3VwIGZpbmRpbmdzIGJ5IHNldmVyaXR5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBncm91cEZpbmRpbmdzQnlTZXZlcml0eShmaW5kaW5nczogRmluZGluZ1tdKTogUmVjb3JkPHN0cmluZywgRmluZGluZ1tdPiB7XG4gIHJldHVybiBmaW5kaW5ncy5yZWR1Y2UoKGdyb3VwcywgZmluZGluZykgPT4ge1xuICAgIGNvbnN0IHNldmVyaXR5ID0gZmluZGluZy5zZXZlcml0eTtcbiAgICBpZiAoIWdyb3Vwc1tzZXZlcml0eV0pIHtcbiAgICAgIGdyb3Vwc1tzZXZlcml0eV0gPSBbXTtcbiAgICB9XG4gICAgZ3JvdXBzW3NldmVyaXR5XS5wdXNoKGZpbmRpbmcpO1xuICAgIHJldHVybiBncm91cHM7XG4gIH0sIHt9IGFzIFJlY29yZDxzdHJpbmcsIEZpbmRpbmdbXT4pO1xufVxuXG4vKipcbiAqIENhbGN1bGF0ZSByaXNrIHNjb3JlIGNvbG9yXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRSaXNrU2NvcmVDb2xvcihzY29yZTogbnVtYmVyKTogc3RyaW5nIHtcbiAgaWYgKHNjb3JlID49IDgwKSByZXR1cm4gJ3RleHQtcmVkLTYwMCc7XG4gIGlmIChzY29yZSA+PSA2MCkgcmV0dXJuICd0ZXh0LW9yYW5nZS02MDAnO1xuICBpZiAoc2NvcmUgPj0gNDApIHJldHVybiAndGV4dC15ZWxsb3ctNjAwJztcbiAgcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCc7XG59XG5cbi8qKlxuICogR2V0IHJpc2sgbGV2ZWwgZnJvbSBzY29yZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Umlza0xldmVsKHNjb3JlOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAoc2NvcmUgPj0gODApIHJldHVybiAnQ3JpdGljYWwnO1xuICBpZiAoc2NvcmUgPj0gNjApIHJldHVybiAnSGlnaCc7XG4gIGlmIChzY29yZSA+PSA0MCkgcmV0dXJuICdNZWRpdW0nO1xuICByZXR1cm4gJ0xvdyc7XG59XG5cbi8qKlxuICogVHJ1bmNhdGUgdGV4dCB3aXRoIGVsbGlwc2lzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0cnVuY2F0ZVRleHQodGV4dDogc3RyaW5nLCBtYXhMZW5ndGg6IG51bWJlcik6IHN0cmluZyB7XG4gIGlmICh0ZXh0Lmxlbmd0aCA8PSBtYXhMZW5ndGgpIHJldHVybiB0ZXh0O1xuICByZXR1cm4gdGV4dC5zdWJzdHJpbmcoMCwgbWF4TGVuZ3RoIC0gMykgKyAnLi4uJztcbn1cblxuLyoqXG4gKiBEZWJvdW5jZSBmdW5jdGlvblxuICovXG5leHBvcnQgZnVuY3Rpb24gZGVib3VuY2U8VCBleHRlbmRzICguLi5hcmdzOiBhbnlbXSkgPT4gYW55PihcbiAgZnVuYzogVCxcbiAgd2FpdDogbnVtYmVyXG4pOiAoLi4uYXJnczogUGFyYW1ldGVyczxUPikgPT4gdm9pZCB7XG4gIGxldCB0aW1lb3V0OiBOb2RlSlMuVGltZW91dDtcbiAgXG4gIHJldHVybiAoLi4uYXJnczogUGFyYW1ldGVyczxUPikgPT4ge1xuICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KTtcbiAgICB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiBmdW5jKC4uLmFyZ3MpLCB3YWl0KTtcbiAgfTtcbn1cblxuLyoqXG4gKiBDb3B5IHRleHQgdG8gY2xpcGJvYXJkXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjb3B5VG9DbGlwYm9hcmQodGV4dDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIHRyeSB7XG4gICAgYXdhaXQgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNvcHkgdG8gY2xpcGJvYXJkOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLyoqXG4gKiBHZW5lcmF0ZSBzY2FuIHJlcG9ydCBmaWxlbmFtZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVSZXBvcnRGaWxlbmFtZShzY2FuUmVzdWx0OiBTY2FuUmVzdWx0LCBmb3JtYXQ6ICdqc29uJyB8ICdjc3YnID0gJ2pzb24nKTogc3RyaW5nIHtcbiAgY29uc3QgdGltZXN0YW1wID0gc2NhblJlc3VsdC50aW1lc3RhbXAudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICBjb25zdCBzb3VyY2UgPSBzY2FuUmVzdWx0LnNvdXJjZVVybCA/IFxuICAgIHNhbml0aXplRmlsZW5hbWUoc2NhblJlc3VsdC5zb3VyY2VVcmwuc3BsaXQoJy8nKS5wb3AoKSB8fCAnc2NhbicpIDogXG4gICAgJ21hbnVhbC1zY2FuJztcbiAgXG4gIHJldHVybiBgbWNwLXNlY3VyaXR5LXNjYW4tJHtzb3VyY2V9LSR7dGltZXN0YW1wfS4ke2Zvcm1hdH1gO1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJmb3JtYXRGaWxlU2l6ZSIsImJ5dGVzIiwiayIsInNpemVzIiwiaSIsIk1hdGgiLCJmbG9vciIsImxvZyIsInBhcnNlRmxvYXQiLCJwb3ciLCJ0b0ZpeGVkIiwiZm9ybWF0UmVsYXRpdmVUaW1lIiwiZGF0ZSIsIm5vdyIsIkRhdGUiLCJkaWZmSW5TZWNvbmRzIiwiZ2V0VGltZSIsIm1pbnV0ZXMiLCJob3VycyIsImRheXMiLCJnZXRTZXZlcml0eUNvbG9yIiwic2V2ZXJpdHkiLCJjb2xvck1hcCIsImNyaXRpY2FsIiwiaGlnaCIsIm1lZGl1bSIsImxvdyIsImdldFNldmVyaXR5QmFkZ2VDb2xvciIsImV4cG9ydFNjYW5SZXN1bHRzIiwic2NhblJlc3VsdCIsIkpTT04iLCJzdHJpbmdpZnkiLCJleHBvcnRTY2FuUmVzdWx0c1RvQ1NWIiwiaGVhZGVycyIsInJvd3MiLCJmaW5kaW5ncyIsIm1hcCIsImZpbmRpbmciLCJydWxlTmFtZSIsImNhdGVnb3J5IiwibGluZSIsInRvU3RyaW5nIiwiY29sdW1uIiwiY29kZSIsInJlcGxhY2UiLCJkZXNjcmlwdGlvbiIsImN3ZSIsImpvaW4iLCJyb3ciLCJkb3dubG9hZEZpbGUiLCJjb250ZW50IiwiZmlsZW5hbWUiLCJjb250ZW50VHlwZSIsImJsb2IiLCJCbG9iIiwidHlwZSIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiaXNWYWxpZEdpdEh1YlVybCIsImdpdGh1YlVybFBhdHRlcm4iLCJ0ZXN0Iiwic2FuaXRpemVGaWxlbmFtZSIsInRvTG93ZXJDYXNlIiwiZ3JvdXBGaW5kaW5nc0J5Q2F0ZWdvcnkiLCJyZWR1Y2UiLCJncm91cHMiLCJwdXNoIiwiZ3JvdXBGaW5kaW5nc0J5U2V2ZXJpdHkiLCJnZXRSaXNrU2NvcmVDb2xvciIsInNjb3JlIiwiZ2V0Umlza0xldmVsIiwidHJ1bmNhdGVUZXh0IiwidGV4dCIsIm1heExlbmd0aCIsImxlbmd0aCIsInN1YnN0cmluZyIsImRlYm91bmNlIiwiZnVuYyIsIndhaXQiLCJ0aW1lb3V0IiwiYXJncyIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJjb3B5VG9DbGlwYm9hcmQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJlcnJvciIsImNvbnNvbGUiLCJnZW5lcmF0ZVJlcG9ydEZpbGVuYW1lIiwiZm9ybWF0IiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInNvdXJjZSIsInNvdXJjZVVybCIsInBvcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"251fbe34c3d2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWNwc2FmZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzZjYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI1MWZiZTM0YzNkMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"MCP Safe - Code Security Scanner\",\n    description: \"Advanced security scanner for detecting dangerous code patterns in MCP and other applications\",\n    keywords: [\n        \"security\",\n        \"code analysis\",\n        \"vulnerability scanner\",\n        \"MCP\",\n        \"GitHub\"\n    ],\n    authors: [\n        {\n            name: \"MCP Safe Team\"\n        }\n    ],\n    openGraph: {\n        title: \"MCP Safe - Code Security Scanner\",\n        description: \"Advanced security scanner for detecting dangerous code patterns\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(rsc)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,CheckCircle,Code,Eye,Github,Shield,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-16 w-16 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 sm:text-5xl\",\n                        children: \"MCP Safe\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                        children: \"Advanced security scanner for detecting dangerous code patterns in MCP applications and repositories. Protect your codebase with rule-based detection, GitHub integration, and AI-powered explanations.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/scan\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Start Scanning\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/github\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Scan Repository\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Rule-Based Detection\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Comprehensive pattern matching for dangerous code\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"JavaScript injection patterns\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"XSS vulnerability detection\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Code obfuscation analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Multi-language support\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-900\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"GitHub Integration\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Scan entire repositories with ease\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Repository URL scanning\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Branch and path support\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"File type filtering\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Batch processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"AI Explanations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"LLM-powered threat analysis and remediation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Detailed vulnerability explanations\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Impact assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Remediation guidance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Security reports\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"Comprehensive Security Coverage\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Our scanner detects a wide range of security vulnerabilities across multiple categories\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            {\n                                name: \"Code Injection\",\n                                count: \"15+\",\n                                color: \"critical\"\n                            },\n                            {\n                                name: \"XSS Vulnerabilities\",\n                                count: \"8+\",\n                                color: \"high\"\n                            },\n                            {\n                                name: \"Data Exfiltration\",\n                                count: \"6+\",\n                                color: \"medium\"\n                            },\n                            {\n                                name: \"Path Traversal\",\n                                count: \"4+\",\n                                color: \"low\"\n                            }\n                        ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                            children: category.count\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: category.color,\n                                            children: \"Detection Rules\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            }, category.name, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_CheckCircle_Code_Eye_Github_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Quick Start Guide\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Get started with MCP Safe in just a few steps\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Manual Code Scanning\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                            className: \"list-decimal list-inside space-y-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Navigate to the Code Scanner page\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Paste your code into the text area\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: 'Click \"Scan Code\" to analyze'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Review findings and export results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/scan\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"Try Code Scanner\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"GitHub Repository Scanning\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                            className: \"list-decimal list-inside space-y-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Go to the GitHub Scanner page\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Enter a GitHub repository URL\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Configure scan options if needed\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Start the repository scan\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/github\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"Try GitHub Scanner\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 rounded-lg p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Trusted by Security Teams\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-600\",\n                                            children: \"50+\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Detection Rules\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-600\",\n                                            children: \"10+\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Languages Supported\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-600\",\n                                            children: \"99%\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Accuracy Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\mcpsafe\src\components\Navigation.tsx#Navigation`);


/***/ }),

/***/ "(rsc)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            critical: \"border-transparent bg-red-100 text-red-800\",\n            high: \"border-transparent bg-orange-100 text-orange-800\",\n            medium: \"border-transparent bg-yellow-100 text-yellow-800\",\n            low: \"border-transparent bg-blue-100 text-blue-800\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9iYWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ21DO0FBQ2pDO0FBRWpDLE1BQU1HLGdCQUFnQkYsNkRBQUdBLENBQ3ZCLDBLQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUNFO1lBQ0ZDLFdBQ0U7WUFDRkMsYUFDRTtZQUNGQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLEtBQUs7UUFDUDtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmVCxTQUFTO0lBQ1g7QUFDRjtBQU9GLFNBQVNVLE1BQU0sRUFBRUMsU0FBUyxFQUFFWCxPQUFPLEVBQUUsR0FBR1ksT0FBbUI7SUFDekQscUJBQ0UsOERBQUNDO1FBQUlGLFdBQVdkLDhDQUFFQSxDQUFDQyxjQUFjO1lBQUVFO1FBQVEsSUFBSVc7UUFBYSxHQUFHQyxLQUFLOzs7Ozs7QUFFeEU7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tY3BzYWZlLy4vc3JjL2NvbXBvbmVudHMvdWkvYmFkZ2UudHN4P2EwMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiO1xuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcblxuY29uc3QgYmFkZ2VWYXJpYW50cyA9IGN2YShcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcm91bmRlZC1mdWxsIGJvcmRlciBweC0yLjUgcHktMC41IHRleHQteHMgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcmluZyBmb2N1czpyaW5nLW9mZnNldC0yXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQgaG92ZXI6YmctcHJpbWFyeS84MFwiLFxuICAgICAgICBzZWNvbmRhcnk6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBob3ZlcjpiZy1kZXN0cnVjdGl2ZS84MFwiLFxuICAgICAgICBvdXRsaW5lOiBcInRleHQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBjcml0aWNhbDogXCJib3JkZXItdHJhbnNwYXJlbnQgYmctcmVkLTEwMCB0ZXh0LXJlZC04MDBcIixcbiAgICAgICAgaGlnaDogXCJib3JkZXItdHJhbnNwYXJlbnQgYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDBcIixcbiAgICAgICAgbWVkaXVtOiBcImJvcmRlci10cmFuc3BhcmVudCBiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMFwiLFxuICAgICAgICBsb3c6IFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDBcIixcbiAgICAgIH0sXG4gICAgfSxcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbik7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQmFkZ2VQcm9wc1xuICBleHRlbmRzIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJhZGdlVmFyaWFudHM+IHt9XG5cbmZ1bmN0aW9uIEJhZGdlKHsgY2xhc3NOYW1lLCB2YXJpYW50LCAuLi5wcm9wcyB9OiBCYWRnZVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKGJhZGdlVmFyaWFudHMoeyB2YXJpYW50IH0pLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4gICk7XG59XG5cbmV4cG9ydCB7IEJhZGdlLCBiYWRnZVZhcmlhbnRzIH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjdmEiLCJjbiIsImJhZGdlVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0Iiwic2Vjb25kYXJ5IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwiY3JpdGljYWwiLCJoaWdoIiwibWVkaXVtIiwibG93IiwiZGVmYXVsdFZhcmlhbnRzIiwiQmFkZ2UiLCJjbGFzc05hbWUiLCJwcm9wcyIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\mcpsafe\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadFile: () => (/* binding */ downloadFile),\n/* harmony export */   exportScanResults: () => (/* binding */ exportScanResults),\n/* harmony export */   exportScanResultsToCSV: () => (/* binding */ exportScanResultsToCSV),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateReportFilename: () => (/* binding */ generateReportFilename),\n/* harmony export */   getRiskLevel: () => (/* binding */ getRiskLevel),\n/* harmony export */   getRiskScoreColor: () => (/* binding */ getRiskScoreColor),\n/* harmony export */   getSeverityBadgeColor: () => (/* binding */ getSeverityBadgeColor),\n/* harmony export */   getSeverityColor: () => (/* binding */ getSeverityColor),\n/* harmony export */   groupFindingsByCategory: () => (/* binding */ groupFindingsByCategory),\n/* harmony export */   groupFindingsBySeverity: () => (/* binding */ groupFindingsBySeverity),\n/* harmony export */   isValidGitHubUrl: () => (/* binding */ isValidGitHubUrl),\n/* harmony export */   sanitizeFilename: () => (/* binding */ sanitizeFilename),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format file size in human readable format\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * Format date in relative time\n */ function formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"just now\";\n    } else if (diffInSeconds < 3600) {\n        const minutes = Math.floor(diffInSeconds / 60);\n        return `${minutes} minute${minutes > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 86400) {\n        const hours = Math.floor(diffInSeconds / 3600);\n        return `${hours} hour${hours > 1 ? \"s\" : \"\"} ago`;\n    } else {\n        const days = Math.floor(diffInSeconds / 86400);\n        return `${days} day${days > 1 ? \"s\" : \"\"} ago`;\n    }\n}\n/**\n * Get severity color for UI components\n */ function getSeverityColor(severity) {\n    const colorMap = {\n        critical: \"text-red-600 bg-red-50 border-red-200\",\n        high: \"text-orange-600 bg-orange-50 border-orange-200\",\n        medium: \"text-yellow-600 bg-yellow-50 border-yellow-200\",\n        low: \"text-blue-600 bg-blue-50 border-blue-200\"\n    };\n    return colorMap[severity] || \"text-gray-600 bg-gray-50 border-gray-200\";\n}\n/**\n * Get severity badge color\n */ function getSeverityBadgeColor(severity) {\n    const colorMap = {\n        critical: \"bg-red-100 text-red-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        medium: \"bg-yellow-100 text-yellow-800\",\n        low: \"bg-blue-100 text-blue-800\"\n    };\n    return colorMap[severity] || \"bg-gray-100 text-gray-800\";\n}\n/**\n * Export scan results to JSON\n */ function exportScanResults(scanResult) {\n    return JSON.stringify(scanResult, null, 2);\n}\n/**\n * Export scan results to CSV\n */ function exportScanResultsToCSV(scanResult) {\n    const headers = [\n        \"Rule Name\",\n        \"Severity\",\n        \"Category\",\n        \"Line\",\n        \"Column\",\n        \"Code\",\n        \"Description\",\n        \"CWE\"\n    ];\n    const rows = scanResult.findings.map((finding)=>[\n            finding.ruleName,\n            finding.severity,\n            finding.category,\n            finding.line.toString(),\n            finding.column?.toString() || \"\",\n            `\"${finding.code.replace(/\"/g, '\"\"')}\"`,\n            `\"${finding.description.replace(/\"/g, '\"\"')}\"`,\n            finding.cwe || \"\"\n        ]);\n    return [\n        headers.join(\",\"),\n        ...rows.map((row)=>row.join(\",\"))\n    ].join(\"\\n\");\n}\n/**\n * Download file with given content\n */ function downloadFile(content, filename, contentType = \"text/plain\") {\n    const blob = new Blob([\n        content\n    ], {\n        type: contentType\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n}\n/**\n * Validate GitHub URL\n */ function isValidGitHubUrl(url) {\n    const githubUrlPattern = /^https?:\\/\\/github\\.com\\/[^\\/]+\\/[^\\/]+/;\n    return githubUrlPattern.test(url);\n}\n/**\n * Sanitize filename for download\n */ function sanitizeFilename(filename) {\n    return filename.replace(/[^a-z0-9.-]/gi, \"_\").toLowerCase();\n}\n/**\n * Group findings by category\n */ function groupFindingsByCategory(findings) {\n    return findings.reduce((groups, finding)=>{\n        const category = finding.category;\n        if (!groups[category]) {\n            groups[category] = [];\n        }\n        groups[category].push(finding);\n        return groups;\n    }, {});\n}\n/**\n * Group findings by severity\n */ function groupFindingsBySeverity(findings) {\n    return findings.reduce((groups, finding)=>{\n        const severity = finding.severity;\n        if (!groups[severity]) {\n            groups[severity] = [];\n        }\n        groups[severity].push(finding);\n        return groups;\n    }, {});\n}\n/**\n * Calculate risk score color\n */ function getRiskScoreColor(score) {\n    if (score >= 80) return \"text-red-600\";\n    if (score >= 60) return \"text-orange-600\";\n    if (score >= 40) return \"text-yellow-600\";\n    return \"text-green-600\";\n}\n/**\n * Get risk level from score\n */ function getRiskLevel(score) {\n    if (score >= 80) return \"Critical\";\n    if (score >= 60) return \"High\";\n    if (score >= 40) return \"Medium\";\n    return \"Low\";\n}\n/**\n * Truncate text with ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength - 3) + \"...\";\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * Generate scan report filename\n */ function generateReportFilename(scanResult, format = \"json\") {\n    const timestamp = scanResult.timestamp.toISOString().split(\"T\")[0];\n    const source = scanResult.sourceUrl ? sanitizeFilename(scanResult.sourceUrl.split(\"/\").pop() || \"scan\") : \"manual-scan\";\n    return `mcp-security-scan-${source}-${timestamp}.${format}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cmcpsafe%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cmcpsafe&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();